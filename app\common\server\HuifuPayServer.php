<?php

namespace app\common\server;

use app\api\logic\PayLogic;
use app\common\enum\ClientEnum;
use app\common\enum\DistributionOrderGoodsEnum;
use app\common\enum\OrderEnum;
use app\common\enum\PayEnum;
use app\common\logic\OrderCommonLogic;
use app\common\model\Client_;
use app\common\model\Config;
use app\common\model\distribution\Distribution;
use app\common\model\distribution\DistributionOrderGoods;
use app\common\model\integral\IntegralOrder;
use app\common\model\order\Order;
use app\common\model\order\OrderGoods;
use app\common\model\order\OrderRefund;
use app\common\model\order\OrderTrade;
use app\common\model\RechargeOrder;
use app\common\model\shop\Shop;
use app\common\model\user\User;
use app\common\model\user\UserAuth;
use app\common\model\user\UserAuthentication;
use app\common\utils\Redis;
use BsPaySdk\config\MerConfig;
use BsPaySdk\core\BsPay;
use BsPaySdk\core\BsPayClient;
use BsPaySdk\request\V2SupplementaryPictureRequest;
use BsPaySdk\request\V2TradeAcctpaymentAcctlogQueryRequest;
use BsPaySdk\request\V2TradePaymentJspayRequest;
use BsPaySdk\request\V2UserBasicdataEntRequest;
use BsPaySdk\request\V2UserBasicdataIndvRequest;
use BsPaySdk\request\V2UserBasicdataQueryRequest;
use BsPaySdk\request\V2TradeSettlementEncashmentRequest;
use BsPaySdk\request\V2TradeAcctpaymentBalanceQueryRequest;
use BsPaySdk\request\V2UserBusiModifyRequest;
use BsPaySdk\request\V2UserListQueryRequest;
use BsPaySdk\request\V3TradePaymentJspayRequest;
use BsPaySdk\request\V3TradePaymentScanpayRefundRequest;
use BsPaySdk\request\V2UserBusiOpenRequest;
use BsPaySdk\request\V2TradePaymentDelaytransConfirmRequest;
use BsPaySdk\request\V2TradeSettlementQueryRequest;
use BsPaySdk\request\V2MerchantBasicdataSettlementQueryRequest;
use BsPaySdk\request\V2TradeBatchtranslogQueryRequest;
use BsPaySdk\request\V2TradePaymentScanpayQueryRequest;
use BsPaySdk\request\V2TradeTransSplitQueryRequest;
use Endroid\QrCode\QrCode;
use think\App;
use think\Exception;
use think\facade\Log;

require_once __DIR__ . '/../../../vendor/huifurepo/dg-php-sdk/BsPaySdk/init.php';
require_once __DIR__ . '/../../../vendor/autoload.php';

/**
 * 微信服务 服务类
 * Class WeChatServer
 * @package app\common\server
 */
class HuifuPayServer
{
    private $initialized = false;
    private $request;
    private $merConfig;

    public $user_id;
    public $userInfo;
    public $client;
    public $from;
    public $pay_way;
    public $shop_id;
    public $pay_partner_account_code;
    public $userAuth;
    public $wechat_config;
    public $wx_data;

    public $request_success;
    public $request_message;
    public $error;
    public $result;
    
    // Add a singleton instance method
    private static $instance;
    
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function getError()
    {
        return $this->error;
    }
    private function init($configKey = 'shop')
    {
        //订单等使用shop,没有值的使用 default_shop
        //平台使用partner
        //平台商户partner_shop
        if (!$this->initialized) {
            $configInfo = config('pay');
            if ($configKey == 'shop'){
                //已经认证的供货商商户
                $shopInfo = Shop::where('id', '=', $this->shop_id)->where('is_open_pay_partner_account', '=', 1)->find();
                if ($shopInfo && isset($shopInfo['is_open_pay_partner_account']) && $shopInfo['is_open_pay_partner_account'] == 1) {
                    $this->merConfig['sys_id'] = $shopInfo['pay_partner_account_code'];
                    $this->merConfig['huifu_id'] = $shopInfo['pay_partner_account_code'];//与sys_id两个相同
                    $this->merConfig['product_id'] = $shopInfo['partner_product_id'];
                    $this->merConfig['rsa_merch_private_key'] = $shopInfo['partner_rsa_merch_private_key'];
                    $this->merConfig['rsa_merch_public_key'] = $shopInfo['partner_rsa_merch_public_key'];
                    $this->merConfig['rsa_huifu_public_key'] = $shopInfo['partner_rsa_huifu_public_key'];
                }else{
                    //默认供货商商户
                    $this->merConfig = $configInfo['is_use_default_shop'] == 1 && isset($configInfo['default_shop']) ? $configInfo['default_shop'] : [];
                }
            }elseif($configKey == 'partner'){   //渠道商商户号
                $this->merConfig = isset($configInfo[$configKey]) ? $configInfo[$configKey] : [];
            }elseif($configKey == 'partner_shop'){  //陌久商户号
                $this->merConfig = isset($configInfo[$configKey]) ? $configInfo[$configKey] : [];
            }
            if (!isset($this->merConfig['sys_id']) || !$this->merConfig['sys_id']){
                $this->error = '汇付支付初始化错误';
                return false;
            }
            $this->pay_partner_account_code = $this->merConfig['sys_id'];
            BsPay::init($this->merConfig, true, 'default');
            $this->initialized = true;
        }
    }

    private function initUser()
    {
        $this->userInfo = User::findOrEmpty($this->user_id)->toArray();
        $wechat_config = [];
        switch ($this->client ?? ClientEnum::mnp) {
            case ClientEnum::mnp:
            case ClientEnum::h5:
            case ClientEnum::ios:
            case ClientEnum::android:
                $this->userAuth = UserAuth::where('user_id', $this->user_id)
                    ->where('client', $this->client)
                    ->order('id desc')
                    ->findOrEmpty()->toArray();
                $wechat_config = WeChatServer::getMnpConfig();
                break;
            case ClientEnum::oa:
            case ClientEnum::pc:
                $this->userAuth = UserAuth::where('user_id', $this->user_id)
                    ->where('client', $this->client)
                    ->order('id desc')
                    ->findOrEmpty()->toArray();
                $wechat_config = WeChatServer::getOaConfig();
                break;
            default:
                break;
        }
        $this->wechat_config = $wechat_config;
    }


    private function getResult($file_data = false)
    {
        $client = new BsPayClient('default');
        Log::write('hfdgPayget$request-------');
        Log::write($this->request);
//        dd($this->request);
        $this->result = $client->postRequest($this->request, $file_data)->getRspDatas();
//        $test_result=[
//            'data' => [
//                'resp_code' => '11111',
//                'resp_desc' => '失败',
//                'huifu_id'  => 'rrrrrr',
//            ]
//        ];
//        $this->result = $test_result;
//        dd($this->result);
        Log::write('hfdgPayget$result-------');
        Log::write($this->result);

        if (!isset($this->result['data']['resp_code'])) {
            $this->request_success = false;
            $this->request_message = $this->request_message ?: '请求失败';
        } else {
            // 00000000 受理成功 00000100 下单成功
            $this->request_success = in_array($this->result['data']['resp_code'], ['00000000', '00000100']);
            $this->request_message = $this->result['data']['resp_desc'];
        }
        return $this->result;
    }


    private function saveOrderResult($order)
    {
        if ($this->request_success) {
            // 记录请求时间等信息
            $update = [
                //回调时候再实际保存重要信息
//                'hf_seq_id'=>$this->result['data']['hf_seq_id'],
                //请求退款时候需要原请求日期等
                'hfdg_params' => json_encode([
                    'pay_request_time' => $_SERVER['REQUEST_TIME'],//请求时间
                    'pay_request_date' => $this->result['data']['req_date'],//请求日期
                    'req_seq_id' => $this->result['data']['req_seq_id'],//商户请求流水号,商户自定义
                    'hf_seq_id' => $this->result['data']['hf_seq_id'],//全局流水号
                    'party_order_id' => $this->result['data']['party_order_id'],//用户账单上的商户订单号
                ]),
            ];
            switch ($this->from) {
                case 'trade':
                    OrderTrade::update($update, [['id', '=', $order['id']]]);
                    Order::update($update, [['trade_id', '=', $order['id']]]);
                    break;
                case 'order':
                    Order::update($update, [['id', '=', $order['id']]]);
                    break;
                case 'recharge':
                    RechargeOrder::update($update, [['id', '=', $order['id']]]);
                    break;
                case 'integral':
                    IntegralOrder::update($update, [['id', '=', $order['id']]]);
                    break;
                default:
                    break;
            }
            $data = '';
            $code = 1;
            if ($this->pay_way == PayEnum::HFDG_WECHAT) {
                switch ($this->client ?? '') {
                    case ClientEnum::mnp:
                    case ClientEnum::oa:
                    case ClientEnum::pc:
                    case ClientEnum::h5:
                    case ClientEnum::ios:
                    case ClientEnum::android:
                        $data = json_decode($this->result['data']['pay_info'], true);
                        break;
                    default:
                        break;
                }
            }
            if ($this->pay_way == PayEnum::HFDG_ALIPAY) {
                $code = 10001;
                switch ($this->client ?? '') {
                    case ClientEnum::pc:
                        $qrCode = new QrCode();
                        $qrCode->setText($this->result["data"]["qr_code"]);
                        $qrCode->setSize(1000);
                        $base64 = chunk_split(base64_encode($qrCode->writeString()));
                        $data = 'data:image/png;base64,' . $base64;
                        break;
                    case ClientEnum::h5:
                    case ClientEnum::ios:
                    case ClientEnum::android:
                        $data = $this->result["data"]["qr_code"];
                        break;
                    default:
                        break;
                }
            }

            $result_data = [
                'code' => $code,
                'msg' => $this->request_message,
                'show' => 0,
                'pay_way' => $this->pay_way,
                'data' => $data,
            ];
            $cache = new Redis();
            $prefix = 'api:unifiedOrder:huifu:orderid' . $order['id'];
            $cache->set($prefix, $result_data, 600);
            return $result_data;
        }

        return [
            'code' => 0,
            'msg' => $this->request_message,
            'show' => 1,
            'pay_way' => $this->pay_way,
            'data' => [],
        ];
    }

    /**
     * 统一下单
     * @param $from
     * @param $order
     * @param $order_source
     * @return \BsPaySdk\core\BsPayRequestV2|null
     */
    public function unifiedOrder($from, $order, $client, $pay_way)
    {
        //缓存上次支付的内容
        $cache = new Redis();
        $prefix = 'api:unifiedOrder:huifu:orderid' . $order['id'];
        $cache_data = $cache->get($prefix);//缓存有效
        if ($cache_data) {
            $this->request_success = true;
            return json_decode($cache_data, true);
        }
        //基础参数初始化
        $this->shop_id = $order['shop_id'] ?? 0;
        $this->init();
        $this->client = $client;
        $this->from = $from;
        $this->user_id = $order['user_id'];
        $this->pay_way = $pay_way;
        $this->initUser();

        //获取分账信息
        if ($from == 'trade') {
            $child_order_id = Order::where('trade_id', '=', $order['id'])->value('id');
        } else {
            $child_order_id = $order['id'];
        }
        $distributionOrderList = PayLogic::distributionOrderGoods($child_order_id, false);
        $acct_split_bunch = $this->getAcctSplitBunch($order, $distributionOrderList);
        //获取商品信息
        $orderGoods = OrderGoods::where('order_id', $child_order_id)->select()->toArray();
        $goods_desc = $orderGoods[0]['goods_name'] ?? '商品列表';
        $goods_desc .= '*' . $orderGoods[0]['goods_num'] ?? '';
        $goods_count = count($orderGoods);
        if ($goods_count >1){
            $goods_desc .= ' 等'.$goods_count.'款商品';
        }
        if ($from == 'order') {
            $order_sn = $order['order_sn'] ? $order['order_sn'] . '_' . date('His') : date('YmdHis') . mt_rand(1000, 9999);        // 请求流水号
        } elseif ($from == 'trade') {
            $order_sn = $order['t_sn'] ? $order['t_sn'] . '_' . date('His') : date('YmdHis') . mt_rand(1000, 9999);        // 请求流水号
        } else {
            $order_sn = date('YmdHis') . mt_rand(1000, 9999);        // 请求流水号
        }
        // 组合请求参数
//        self::$request = new V2TradePaymentJspayRequest();
        $this->request = new V3TradePaymentJspayRequest();
        $this->request->setReqSeqId($order_sn);
        // 请求日期
        $this->request->setReqDate(date("Ymd"));
        // 商户号 不同供货商不同商户号, 没有的使用默认
//        self::$request->setHuifuId(self::$merConfig['huifu_id']);
        $this->request->setHuifuId($this->pay_partner_account_code); //供货商商户号,陌久是****************
        // 交易金额
        $this->request->setTransAmt($order['order_amount']);
        // 商品描述
        $this->request->setGoodsDesc($goods_desc);
        // 交易类型
        $this->request->setTradeType('T_MINIAPP');

        $this->wx_data = [
            'sub_appid' => $this->wechat_config['app_id'] ?? '',//	子商户应用ID
            'sub_openid' => $this->userAuth['openid'] ?? '', //子商户openid
//            'openid'=> self::$userAuth['openid'] ?? '', //子商户openid
//            'pay_scene'=>'02',
//            'channel_no'=>self::$merConfig['huifu_id']
        ];

        $extendInfo = [
            'fee_flag'=>config('pay.partner_fee_flag', '1'),
            'delay_acct_flag' => 'Y',  //是否延迟入账 分账
            'remark' => implode('-', [
                PayEnum::HFDG_WECHAT,
                $client,
                $from,
                $order['id'],
            ]),
            'notify_url' => (string)url('pay/hfdgPayWechatNotify', [], false, true),
//            'wx_data' => json_encode(self::$wx_data),
            'wx_data' => $this->wx_data,
        ];
        $extendInfo['acct_split_bunch'] = json_encode($acct_split_bunch);
        $this->request->setExtendInfo($extendInfo);
        // 发起请求
        $this->getResult();
        if ($this->request_success) {
            return $this->saveOrderResult($order);
        } else {
            $this->error = $this->request_message;
        }
        return false;
    }


    /**
     * 订单查询
     * https://paas.huifu.com/open/doc/api/#/smzf/api_qrpay_cx_v3?id=miniwx
     * v3/trade/payment/scanpay/query
     */
    public function orderQuery($order){
        $this->shop_id = $order['shop_id'];
        $this->init();
        $this->initUser();
        $this->request = new V2TradePaymentScanpayQueryRequest();
        $this->request->setHuifuId($this->pay_partner_account_code);
        $this->request->setOrgHfSeqId($order['hf_seq_id']);
        $this->getResult();
        if ($this->request_success && $this->result['data']) {
            if (isset($this->result['data']['fee_formula_infos'])){
                $this->result['data']['fee_formula_infos'] = json_decode($this->result['data']['fee_formula_infos'], true);
            }
            if (isset($this->result['data']['wx_response'])){
                $this->result['data']['wx_response'] = json_decode($this->result['data']['wx_response'], true);
            }
            return $this->result['data'];
        }
        $this->error = $this->request_message;
        return false;
    }


    /**
     * 交易分账明细查询
     * https://paas.huifu.com/open/doc/api/#/smzf/api_fzmxcx
     * @param $order
     * @param $refund_order
     * @return array|bool 分账明细数组或false
     */
    public function transSplitQuery($order)
    {
        $this->shop_id = $order['shop_id'];
        $this->init();
        $this->request = new V2TradeTransSplitQueryRequest();
        $this->request->setHuifuId($this->pay_partner_account_code);
        $this->request->setHfSeqId($order['hf_seq_id']);
        $this->request->setOrdType('consume');  //consume：正向交易，refund：反向交易；
        
        // 保存当前状态
        $originalSuccess = $this->request_success;
        $originalResult = $this->result;
        
        $this->getResult();
        $success = $this->request_success;
        $result = $this->result;
        
        // 恢复原始状态
        $this->request_success = $originalSuccess;
        $this->result = $originalResult;
        
        if ($success && isset($result['data'])) {
            if (isset($result['data']['split_trans_responses'])){
                $result['data']['split_trans_responses'] = json_decode($result['data']['split_trans_responses'], true);
                return $result['data']['split_trans_responses'];
            }
            return [];
        }
        return false;
    }


    /**
     * 完成一个分销订单的结算
     * @param array $order 主订单信息
     * @param array $oneDistributionOrder 单个分销订单信息
     * @return mixed 结果
     */
    public function distributionOrderConfirm($oneDistributionOrder, $acct_split_bunch)
    {
        $oneDistributionOrder['huifu_id'] = Distribution::where('user_id', $oneDistributionOrder['user_id'])->value('pay_partner_account_code');
        if (!$oneDistributionOrder['huifu_id']){
            $this->error = '分账方商户号不存在';
            return false;
        }
        $this->shop_id = $oneDistributionOrder['shop_id'];
        $this->init();
        
        $this->request = new V2TradePaymentDelaytransConfirmRequest();
        // 请求流水号
        $this->request->setReqSeqId($oneDistributionOrder['sn']);//使用分销单单号作为流水号, 当天不允许重复 防止重复
        // 请求日期
        $this->request->setReqDate(date("Ymd"));
        // 商户号
        $this->request->setHuifuId($this->pay_partner_account_code);
        // 支付方式
        $this->request->setPayType('QUICK_PAY');
        $extendInfo = [
            'acct_split_bunch' => json_encode($acct_split_bunch),
            'org_hf_seq_id' => $oneDistributionOrder['hf_seq_id']
        ];
        $this->request->setExtendInfo($extendInfo);

        // 发起请求
        $this->getResult();
        if ($this->request_success && isset($this->result['data']['acct_split_bunch'])) {
                $this->result['data']['acct_split_bunch'] = json_decode($this->result['data']['acct_split_bunch'], true);
                OrderCommonLogic::completeDistributionOrderSettle($oneDistributionOrder);
                return $this->result['data']['acct_split_bunch'];
        }
        $this->error = $this->request_message ?: '分账失败';
        return false;
    }

    public function formatOneDistributionOrder()
    {

    }

    /**
     * 为单个分销订单组合分账信息
     * @param array $order 主订单信息
     * @param array $oneDistributionOrder 单个分销订单信息
     * @param array $huifu_order 汇付订单查询结果
     * @return array 分账信息
     */
    public function getAcctSplitBunchForSingleDistribution($oneDistributionOrder)
    {
        $total_able_split_amount = $huifu_order['unconfirm_amt'] ?? $oneDistributionOrder['money'];
        $acct_split_bunch = ['acct_infos' => []];
        
        // 添加分销商分账信息
        if ($oneDistributionOrder['money'] > 0) {
            $distribution_info = Distribution::where('is_distribution', '=', 1)
                ->where('user_id', '=', $oneDistributionOrder['user_id'])
                ->findOrEmpty()
                ->toArray();
            
            if ($distribution_info && isset($distribution_info['pay_partner_account_code']) && $distribution_info['pay_partner_account_code']) {
                $acct_split_bunch['acct_infos'][] = [
                    'huifu_id' => $distribution_info['pay_partner_account_code'],
                    'div_amt' => $oneDistributionOrder['money']
                ];
            }
        }

        $acct_split_bunch['percentage_flag'] = 'N';
        $acct_split_bunch['total_div_amt'] = $total_able_split_amount;
        
        return $acct_split_bunch;
    }


    /**
     * 用户评价之后分账给平台手续费,货款分账给供货商
     */
    public function platformSharing($order, $acct_split_bunch)
    {
        $this->shop_id = $order['shop_id'];
        $this->init();
//        $this->initUser();
        $this->request = new V2TradePaymentDelaytransConfirmRequest();
        // 请求流水号
        $this->request->setReqSeqId(date('YmdHis') . mt_rand(1000, 9999));
        // 请求日期
        $this->request->setReqDate(date("Ymd"));
        // 商户号
        $this->request->setHuifuId($this->pay_partner_account_code);
        // 支付方式
        $this->request->setPayType('QUICK_PAY');

        $extendInfo = [
            'acct_split_bunch' => json_encode($acct_split_bunch),
            'org_hf_seq_id' => $order['hf_seq_id']
        ];
        $this->request->setExtendInfo($extendInfo);
        // 发起请求
        $this->getResult();
//        dd($this->result);
        if ($this->request_success) {
            Log::write('orderConfirm-结果----');
            Log::write($this->result);
        }
        return $this->result;
    }

    /**
     * 组合分账信息
     * @param $distributionOrderList
     */
    public function getAcctSplitBunch($order, $distributionOrderList, $huifu_order=[])
    {
        $total_able_split_amount = $huifu_order['unconfirm_amt'] ?? $order['order_amount'];   //本次总共可分账金额
        $acct_split_bunch['acct_infos'] = [];
        if ($distributionOrderList && is_array($distributionOrderList)) {
            $distributionOrderList = array_values($distributionOrderList);
            foreach ($distributionOrderList as $key => $dis_order) {
                if ($dis_order['money'] > 0) {
                    $distribution_info = Distribution::where('is_distribution', '=', 1)->where('user_id', '=', $dis_order['user_id'])->findOrEmpty()->toArray();
                    if (!$distribution_info || !isset($distribution_info['pay_partner_account_code']) || !$distribution_info['pay_partner_account_code']) {
                        continue;
                    }
                    $huifu_id = $distribution_info['pay_partner_account_code'];
                    //如果第二个和第一个是同一个供货商,则合并
                    if ($key == 1 && $acct_split_bunch['acct_infos'][0]['huifu_id'] == $huifu_id) {
                        $acct_split_bunch['acct_infos'][0]['div_amt'] = moneyFormat($acct_split_bunch['acct_infos'][0]['div_amt'] + $dis_order['money']);
                    } else {
                        $acct_split_bunch['acct_infos'][$key]['huifu_id'] = $huifu_id;
                        $acct_split_bunch['acct_infos'][$key]['div_amt'] = $dis_order['money'];
                    }
                }
            }
        }
        //平台服务费分账账户
        $platform_distribution_percentage = config('pay.platform_distribution_percentage', 0);
        if ($platform_distribution_percentage > 0) {
            $acct_split_bunch['acct_infos'][] = [
                'huifu_id' => config('pay.platform_distribution_huifu_id', ''),
                'div_amt' => moneyFormat($total_able_split_amount * $platform_distribution_percentage / 100)
            ];
        }
//        $pay_partner_account_code = isset($order['shop_info']['pay_partner_account_code']) && $order['shop_info']['pay_partner_account_code'] ? $order['shop_info']['pay_partner_account_code'] : '';
//        $pay_partner_account_code =  !$pay_partner_account_code ? Shop::field('pay_partner_account_code,is_open_pay_partner_account')->where('id', '=', $order['shop_id'])->where('is_open_pay_partner_account', '=', 1)->value('pay_partner_account_code') : '';
        //供货商货款分账账户 剩余货款
        $acct_split_bunch['acct_infos'][] = [
            'huifu_id' => $this->pay_partner_account_code,
            'div_amt' => moneyFormat($total_able_split_amount - array_sum(array_column($acct_split_bunch['acct_infos'], 'div_amt')))
        ];
        //是否扣除手续费
//        if ($is_minus_fee) {
//            foreach ($acct_split_bunch['acct_infos'] as $key => &$val) {
////                $pay_fee = moneyFormat($val['div_amt'] * 0.65 / 100);
////                $pay_fee = $pay_fee >= 0.01 ? $pay_fee : 0.01;
////
//                if ($key == 2) {
//                    $val['div_amt'] = moneyFormat($val['div_amt'] - 0.02);
//                }
//
//            }
//        }
        $acct_split_bunch['percentage_flag'] = 'N';
        $acct_split_bunch['total_div_amt'] = $total_able_split_amount;
        return $acct_split_bunch;
    }

    public function refund($order, $refund_data)
    {
        $this->shop_id = $order['shop_id'];
        $this->init();
//        $this->user_id = $order['user_id'];
        if (!$this->pay_partner_account_code){
            return '未配置供货商汇付账户';
        }
        $order_sn = $order['order_sn'] ? $order['order_sn'] . '_' . date('His') : '';        // 请求流水号
        $hfdg_params = isset($order['hfdg_params']) && $order['hfdg_params'] ? json_decode($order['hfdg_params'], true) : [];
        if (!$hfdg_params){
            return '未获取订单交易数据';
        }
        $this->request = new V3TradePaymentScanpayRefundRequest();
        // 请求日期
        $this->request->setReqDate(date("Ymd"));
        // 请求流水号req_seq_id
        $this->request->setReqSeqId($order_sn);
        // 商户号
        $this->request->setHuifuId($this->pay_partner_account_code);
        // 退款金额
        $this->request->setOrdAmt(moneyFormat($refund_data['refund_fee']));
        // 原交易日期orig_req_date
        $this->request->setOrgReqDate($hfdg_params['pay_request_date']);
        $extendInfo = [
            'org_hf_seq_id' => $hfdg_params['hf_seq_id'] ?? '',   //原交易全局流水号
            'notify_url' => (string)url('pay/hfdgRefundWechatNotify', [], false, true),
        ];
        $this->request->setExtendInfo($extendInfo);

//        dd($this->request);

        // 发起请求
        $this->getResult();

        Log::write('hfdgPaygetrefund-结果----');
        Log::write($this->result);

        if ($this->request_success && isset($this->result['data']['hf_seq_id'])) {
            OrderRefund::where('id', '=', $refund_data['refund_id'])->update([
                'refund_transaction_id' => $this->result['data']['hf_seq_id'],  //退款交易流水号
            ]);
            return true;
        } else {
            $this->error = $this->request_message ?? '未知错误';
        }
        return $this->error;
    }

    /**
     * 图片上传汇付
     * @param $file
     * @return void
     */
    public function upload($file_data, $file)
    {
        //平台上传
        $this->init('partner_shop');
        $this->user_id = $file_data['user_id'];
        $this->initUser();

        //V2SupplementaryPictureRequest
        $this->request = new V2SupplementaryPictureRequest();
        // 请求日期
        $this->request->setReqDate(date("Ymd"));
        // 请求流水号req_seq_id
        $reqSeqId = date('YmdHis') . mt_rand(1000, 9999);
        $this->request->setReqSeqId($reqSeqId);
        // 图片类型
        $this->request->setFileType($file_data['file_type']);
        // 图片名称
//        $this->request->setPicture($file_data['uri']);
        $extendInfo = [
//            'huifu_id'=> $hfdg_params['hf_seq_id'] ?? '', //渠道与一级代理商的直属商户ID；示例值：6666000123123123 //如果商户未开户没有商户号，可以为空。
            'file_url' => $file_data['uri'],   //原交易请求流水号
        ];
        $this->request->setExtendInfo($extendInfo);
        // 发起请求
        $this->getResult($file);
        if ($this->request_success && isset($this->result['data']['file_id'])) {
            return $this->result['data'];
        } else {
            $this->error = $this->request_message ?? '未知错误';
        }
        return false;
    }

    /**
     * 处理认证入驻结果
     * @param $authentication
     * @return array|mixed
     */
    private function updateAuth($authentication)
    {
        $time = time();
        if ($this->request_success && isset($this->result['data']['huifu_id'])) {
            $update = [
                'user_code' => $this->result['data']['huifu_id'],
                'auth_status' => 1,
                'auth_result' => json_encode($this->result['data']),
                'update_time' => $time,
                'success_time' => $time
            ];
            UserAuthentication::where('id', '=', $authentication['id'])->update($update);
            return $this->result['data'];
        } else {
            $this->error = $this->request_message ?? '未知错误';
        }
        $update = [
            'auth_status' => 2,
            'auth_result' => json_encode($this->result['data']),
//            'auth_param'=>json_encode($this->result['data']),
            'update_time' => $time
        ];
        UserAuthentication::where('id', '=', $authentication['id'])->update($update);
        return false;
    }


    /**
     * 分销商--企业进件
     * @param $order
     * @param $client
     * @param $from
     */
    public function reg_distribution_ent($authentication)
    {
        if ($authentication['user_code']) {
            $this->error = '已经存在商户号' . $authentication['user_code'] . '请勿重复提交';
            return false;
        }
        $this->init('partner_shop');
        $this->user_id = $authentication['user_id'];
        $this->initUser();
        $this->request = new V2UserBasicdataEntRequest();
        // 请求日期
        $this->request->setReqDate(date("Ymd"));
        // 请求流水号req_seq_id
        $reqSeqId = date('YmdHis') . mt_rand(1000, 9999);
        $this->request->setReqSeqId($reqSeqId);
        // 公司信息
        // 商户公司名称,个人无需填写
        $this->request->setRegName($authentication['reg_name']);
        // 营业执照
        $this->request->setLicenseCode($authentication['license_code']);
        $this->request->setLicenseValidityType($authentication['license_validity_type']);
        $this->request->setLicenseBeginDate($authentication['license_begin_date']);
        if ($authentication['license_validity_type'] != 1) { //非长期
            $this->request->setLicenseEndDate($authentication['license_end_date']);
        }
        // 注册地址(省)
        $this->request->setRegProvId($authentication['reg_prov_id']);
        // 注册地址(市)
        $this->request->setRegAreaId($authentication['reg_area_id']);
        // 注册地址(区)
        $this->request->setRegDistrictId($authentication['reg_district_id']);
        // 注册地址(详细地址)
        $this->request->setRegDetail($authentication['reg_detail']);

        // 法人姓名
        $this->request->setLegalName($authentication['legal_name']);
        // 法人证件类型
        $this->request->setLegalCertType($authentication['legal_cert_type']);
        // 法人身份证号
        $this->request->setLegalCertNo($authentication['legal_cert_no']);
        // 法人证件有效期类型
        $this->request->setLegalCertValidityType($authentication['legal_cert_validity_type']);
        // 法人身份证有效期
        $this->request->setLegalCertBeginDate($authentication['legal_cert_begin_date']);
        if ($authentication['legal_cert_validity_type'] != 1) {
            $this->request->setLegalCertEndDate($authentication['legal_cert_end_date']);
        } else {
            $this->request->setLegalCertEndDate('');
        }
        // 联系人姓名
        $this->request->setContactName($authentication['contact_name']);
        // 联系人手机号
        $this->request->setContactMobile($authentication['contact_mobile_no']);

        // 根据API文档设置扩展信息
        $extendInfo = [];

        // 商户简称
        if (!empty($authentication['short_name'])) {
            $extendInfo['short_name'] = $authentication['short_name'];
        }

        // 企业类型 (公司类型)
        if (!empty($authentication['ent_type'])) {
            $extendInfo['ent_type'] = $authentication['ent_type'];
        }

        // 成立日期
        if (!empty($authentication['found_date'])) {
            $extendInfo['found_date'] = $authentication['found_date'];
        }

        // 法人国籍
        $extendInfo['legal_cert_nationality'] = 'CHN'; // 默认中国

        // 注册资本
        if (!empty($authentication['reg_capital'])) {
            $extendInfo['reg_capital'] = $authentication['reg_capital'];
        }

        // 所属行业
        if (!empty($authentication['mcc'])) {
            $extendInfo['mcc'] = $authentication['mcc'];
        }

        // 文件列表
        $fileList = [];

        // 营业执照照片
        if (!empty($authentication['license_pic'])) {
            $fileList[] = [
                'file_type' => 'F07', // 营业执照
                'file_id' => $authentication['license_pic']
            ];
        }

        // 法人身份证正面
        if (!empty($authentication['legal_cert_front_pic'])) {
            $fileList[] = [
                'file_type' => 'F02', // 法人身份证人像面
                'file_id' => $authentication['legal_cert_front_pic']
            ];
        }

        // 法人身份证反面
        if (!empty($authentication['legal_cert_back_pic'])) {
            $fileList[] = [
                'file_type' => 'F03', // 法人身份证国徽面
                'file_id' => $authentication['legal_cert_back_pic']
            ];
        }

        // 只有当有文件时才添加file_list字段
        if (!empty($fileList)) {
            $extendInfo['file_list'] = json_encode($fileList);
        }

        // 是否发送短信标识
        $extendInfo['sms_send_flag'] = 'N'; // 默认不发送

        // 设置扩展信息
        $extendInfo['expand_id'] = $this->pay_partner_account_code;

        // 设置扩展信息
        if (!empty($extendInfo)) {
            $this->request->setExtendInfo($extendInfo);
        }

        // 打印请求对象，方便调试
//        dd($this->request);
        // 发起请求
        $this->getResult();
        //更新数据库信息
        $this->updateAuth($authentication);
        if ($this->request_success) {
            return true;
        } else {
            $this->error = $this->request_message ?? '未知错误';
        }
        return false;
    }

    /**
     * 分销商--个人进件
     * V2UserBasicdataIndvRequest
     */
    public function reg_distribution_user($authentication)
    {
        if ($authentication['user_code']) {
            $this->error = '已经存在商户号' . $authentication['user_code'] . '请勿重复提交';
            return false;
        }
        $this->init('partner_shop');
        $this->user_id = $authentication['user_id'];
        $this->initUser();
        $this->request = new V2UserBasicdataIndvRequest();
        // 请求日期
        $this->request->setReqDate(date("Ymd"));
        // 请求流水号req_seq_id
        $reqSeqId = date('YmdHis') . mt_rand(1000, 9999);
        $this->request->setReqSeqId($reqSeqId);
        // 个人姓名
        $this->request->setName($authentication['legal_name']);
        // 个人证件类型
        $this->request->setCertType($authentication['legal_cert_type']);
        // 个人身份证号
        $this->request->setCertNo($authentication['legal_cert_no']);
        // 个人身份证有效期类型
        $this->request->setCertValidityType($authentication['legal_cert_validity_type']);
        // 个人身份证有效期
        $this->request->setCertBeginDate($authentication['legal_cert_begin_date']);
//        if ($authentication['cert_validity_type'] != 1){
//            $this->request->setCertEndDate($authentication['cert_end_date']);
//        }
        // 个人手机号
        $this->request->setMobileNo($authentication['legal_mobile_no']);
        $extendInfo = [
            'cert_end_date' => $authentication['legal_cert_end_date'],   //结束日期
            'expand_id' => $this->pay_partner_account_code
        ];
        $this->request->setExtendInfo($extendInfo);

        // 发起请求
        $this->getResult();
        $this->updateAuth($authentication);
        if ($this->request_success) {
            return true;
        } else {
            $this->error = $this->request_message ?? '未知错误';
        }
        return false;
    }

    public function reg_shop_ent($authentication)
    {

    }

    /**
     * 商户进件, 业务完善
     * https://paas.huifu.com/open/doc/api/#/yhgl/api_yhgl_ywrz
     * @param $authentication
     */
    public function userBusiOpen($authentication)
    {
        //初始化使用陌久商户下
        $this->init('partner_shop');
        $this->user_id = $authentication['user_id'];
        $this->initUser();
        $this->request = new V2UserBusiOpenRequest();
        // 请求日期
        $this->request->setReqDate(date("Ymd"));
        // 请求流水号req_seq_id
        $reqSeqId = date('YmdHis') . mt_rand(1000, 9999);
        $this->request->setReqSeqId($reqSeqId);
        // 汇付ID
        $this->request->setHuifuId($authentication['user_code']);
        //渠道商/商户汇付Id
//        $this->request->setUpperHuifuId($this->merConfig['huifu_id']);
        $this->request->setUpperHuifuId($this->pay_partner_account_code); //统一放到陌久商户下??
        //二业务信息配置
        if ($authentication['card_info']) {//必须有卡才能配置 结算信息
            //2.1 	结算卡信息 card_info
            $extendInfo['card_info'] = $authentication['card_info'];
            //2.2 结算信息配置
            $extendInfo['settle_config'] = json_encode([
                'settle_cycle' => 'T1', // 结算周期
                'min_amt' => '1', // 起结金额
                'remained_amt' => '0.00', // 留存金额
                'settle_abstract' => '',  //	结算摘要
                'out_settle_flag' => '2',//手续费外扣标记1：外扣；2：内扣(为空时默认值)；
//            'settle_pattern'=>'P0',    //P0：批次结算（为空时默认值）， P1：定时结算(建议选P0和P2)，P2:批次定时结算；
//            'settle_batch_no'=>'100',  //结算批次说明 https://paas.huifu.com/open/doc/api/#/csfl/api_csfl_jspc
                'fixed_ratio' => '0.00', //节假日结算手续费率
                'workday_fixed_ratio' => '0.60',//工作日结算手续费率 单位%，需保留小数点后两位。取值范围[0.00，100.00]，不填默认为0.00；
                'workday_constant_amt' => '0.00', // 工作日结算手续费固定金额
            ]);
            //2.3 cash_config	取现配置列表
            $extendInfo['cash_config'] = json_encode([
                [
                    'cash_type' => 'T1', //业务类型  T1:下一工作日到银行账户；D1：下一自然日到银行账户；D0：当日到银行账户；默认D0；DM：当日到账；到账资金不包括当天的交易资金； 0：不可取现；1：可取现
                    'fix_amt' => '0.00', //固定金额 提现手续费（固定/元）
                    'fee_rate' => '0.60', //费率 提现手续费（费率/%）
                    'out_fee_flag' => '2', //是否交易手续费外扣 1:外扣 2:内扣（默认2内扣）
                    //'out_fee_huifu_id'=>''//手续费外扣时必需指定手续费承担方ID
                ]
            ]);
        }
        //2.4 	文件列表 当资料异常 企业用户card_name与reg_name不一致时需上传 file_list
//        $extendInfo['file_list'] = [];
        $extendInfo['delay_flag'] = 'Y';    //延迟入账开关N：否 Y：是；示例值：
        $this->request->setExtendInfo($extendInfo);
        $this->getResult();
        $time = time();
        if ($this->request_success && isset($this->result['data'])) {
            $update = [
                'user_code' => $this->result['data']['huifu_id'],
                'auth_status' => 10,
                'auth_result' => json_encode($this->result['data']),
                'update_time' => $time,
                'success_time' => $time,
                'extend_info' => json_encode($extendInfo)
            ];
            UserAuthentication::where('id', '=', $authentication['id'])->update($update);
            $update_code =[
                'pay_partner_account_code' => $update['user_code'],
                'is_open_pay_partner_account'=>1
            ];
            Distribution::where('user_id', '=', $this->user_id)->update($update_code);
            return $this->result['data'];
        }else{
            $update = [
                'auth_status' => 3,
                'auth_result' => json_encode($this->result['data']),
                'update_time' => $time,
                'extend_info' => json_encode($extendInfo)
            ];
            UserAuthentication::where('id', '=', $authentication['id'])->update($update);
            $this->error = $this->request_message ?? '银行卡提交认证失败';
            return false;
        }
    }

    /**
     * 用户业务入驻修改
     * 接口文档 * https://paas.huifu.com/open/doc/api/#/yhgl/api_yhgl_ywrzxg
     */
    public function userBusiModify($authentication){
        //初始化使用陌久商户下
        $this->init('partner_shop');
        $this->user_id = $authentication['user_id'];
        $this->initUser();
        $this->request = new V2UserBusiModifyRequest();
        // 请求日期
        $this->request->setReqDate(date("Ymd"));
        // 请求流水号req_seq_id
        $reqSeqId = date('YmdHis') . mt_rand(1000, 9999);
        $this->request->setReqSeqId($reqSeqId);
        // 汇付ID
        $this->request->setHuifuId($authentication['user_code']);
        //渠道商/商户汇付Id
//        $this->request->setUpperHuifuId($this->merConfig['huifu_id']);
        $this->request->setUpperHuifuId($this->pay_partner_account_code); //统一放到陌久商户下??
        //二业务信息配置
        if ($authentication['card_info']) {//必须有卡才能配置 结算信息
            //2.1 	结算卡信息 card_info
            $extendInfo['card_info'] = $authentication['card_info'];
            //2.2 结算信息配置
            $extendInfo['settle_config'] = json_encode([
                'settle_status'=>'1',   //开通状态
                'settle_cycle' => 'T1', // 结算周期
                'min_amt' => '1', // 起结金额
                'remained_amt' => '0.00', // 留存金额
                'settle_abstract' => '',  //	结算摘要
                'out_settle_flag' => '2',//手续费外扣标记1：外扣；2：内扣(为空时默认值)；
//            'settle_pattern'=>'P0',    //P0：批次结算（为空时默认值）， P1：定时结算(建议选P0和P2)，P2:批次定时结算；
//            'settle_batch_no'=>'100',  //结算批次说明 https://paas.huifu.com/open/doc/api/#/csfl/api_csfl_jspc
                'fixed_ratio' => '0.00', //节假日结算手续费率
                'workday_fixed_ratio' => '0.00',//工作日结算手续费率 单位%，需保留小数点后两位。取值范围[0.00，100.00]，不填默认为0.00；
                'workday_constant_amt' => '0.00', // 工作日结算手续费固定金额
            ]);
            //2.3 cash_config	取现配置列表
            $extendInfo['cash_config'] = json_encode([
                [
                    'switch_state'=>'1', //开通状态
                    'cash_type' => 'T1', //业务类型  T1:下一工作日到银行账户；D1：下一自然日到银行账户；D0：当日到银行账户；默认D0；DM：当日到账；到账资金不包括当天的交易资金； 0：不可取现；1：可取现
                    'fix_amt' => '0.00', //固定金额 提现手续费（固定/元）
                    'fee_rate' => '0.6', //费率 提现手续费（费率/%）
                    'out_fee_flag' => '2', //是否交易手续费外扣 1:外扣 2:内扣（默认2内扣）
                    //'out_fee_huifu_id'=>''//手续费外扣时必需指定手续费承担方ID
                ]
            ]);
        }
        //2.4 	文件列表 当资料异常 企业用户card_name与reg_name不一致时需上传 file_list
//        $extendInfo['file_list'] = [];
        $extendInfo['delay_flag'] = 'Y';    //延迟入账开关N：否 Y：是；示例值：
        $this->request->setExtendInfo($extendInfo);
        $this->getResult();
        $time = time();
        if ($this->request_success && isset($this->result['data'])) {
            $update = [
                'user_code' => $this->result['data']['huifu_id'],
                'auth_status' => 10,
                'auth_result' => json_encode($this->result['data']),
                'update_time' => $time,
                'success_time' => $time,
                'extend_info' => json_encode($extendInfo)
            ];
            UserAuthentication::where('id', '=', $authentication['id'])->update($update);
            $update_code =[
                'pay_partner_account_code' => $update['user_code'],
                'is_open_pay_partner_account'=>1
            ];
            Distribution::where('user_id', '=', $this->user_id)->update($update_code);
            return $this->result['data'];
        }else{
            $update = [
                'auth_status' => 3,
                'auth_result' => json_encode($this->result['data']),
                'update_time' => $time,
                'extend_info' => json_encode($extendInfo)
            ];
            UserAuthentication::where('id', '=', $authentication['id'])->update($update);
            $this->error = $this->request_message ?? '银行卡提交认证失败';
            return false;
        }

    }

    /**
     * 用户列表查询
     * @param $query
     * @return array
     */
    public function userList($query)
    {
        $this->init('partner');
        $this->request = new V2UserListQueryRequest();
        // 请求日期
        $this->request->setReqDate(date("Ymd"));
        // 请求流水号req_seq_id
        $reqSeqId = date('YmdHis') . mt_rand(1000, 9999);
        $this->request->setReqSeqId($reqSeqId);
        $this->request->setLegalCertNo($query['legal_cert_no']);
        $upper_huifu_id = $query['upper_huifu_id'] ?? '';
        if ($upper_huifu_id) {
            $extendInfo = [
                'upper_huifu_id' => $upper_huifu_id,
            ];
            $this->request->setExtendInfo($extendInfo);
        }
        $this->getResult();
        if ($this->request_success && isset($this->result['data']['user_list_info_list'])) {
            return $this->result['data']['user_list_info_list'];
        }
        return [];
    }

    /**
     * 用户信息查询
     * @param $query
     * @return array
     */
    public function userQuery($query)
    {
        $this->init('partner');
        $this->request = new V2UserBasicdataQueryRequest();
        // 请求日期
        $this->request->setReqDate(date("Ymd"));
        // 请求流水号req_seq_id
        $reqSeqId = date('YmdHis') . mt_rand(1000, 9999);
        $this->request->setReqSeqId($reqSeqId);
        $this->request->setHuifuId($query['huifu_id']);
        $this->getResult();
        if ($this->request_success && isset($this->result['data'])) {
            foreach ($this->result['data'] as $key => $datum) {
                if ($datum && is_string($datum)) {
                    $data_arr = json_decode($datum, true);
                    if ($data_arr && is_array($data_arr)) {
                        $this->result['data'][$key] = $data_arr;
                    }
                }
            }
            return $this->result['data'];
        }
        return [];
    }

    /**
     * 用户提现
     */
    public function userEncashment($query)
    {
        $this->init('partner');
        $this->request = new V2TradeSettlementEncashmentRequest();
        // 请求日期
        $this->request->setReqDate(date("Ymd"));
        // 请求流水号req_seq_id
        $reqSeqId = date('YmdHis') . mt_rand(1000, 9999);
        $this->request->setReqSeqId($reqSeqId);
        $this->request->setHuifuId($query['huifu_id']);
        $this->request->setCashAmt($query['cash_amt']);
        $this->request->setIntoAcctDateType('T1');
        //token_no
        $this->request->setTokenNo($query['token_no']);
        $this->getResult();
        if ($this->request_success && isset($this->result['data'])) {
            return $this->result['data'];
        }
    }

    /**
     * 用户取现状态及明细
     */
    public function userEncashmentQuery($query){
        $this->init('partner');
        $this->request = new V2TradeSettlementQueryRequest();
        //汇付客户Id
        $this->request->setHuifuId($query['huifu_id']);
        //原交易请求日期
        $this->request->setOrgReqDate($query['org_req_date']);
        //原交易返回的全局流水号
        $this->request->setOrgHfSeqId($query['org_hf_seq_id']);
        //原交易请求流水号原交易返回的全局流水号、原交易请求流水号二选一必填；
//        $this->request->setOrgReqSeqId($query['org_req_seq_id']);
        $this->getResult();
        if ($this->request_success && isset($this->result['data'])) {
            return $this->result['data'];
        }
    }

    /**
     * 用户结算信息查询
     * https://paas.huifu.com/open/doc/api/#/jyjs/qx/api_jsjlfycx
     */
    public function userSettlementQuery($query){
        //V2MerchantBasicdataSettlementQueryRequest
        $this->init('partner');
        $this->request = new V2MerchantBasicdataSettlementQueryRequest();
        // 请求日期
        $this->request->setReqDate(date("Ymd"));
        // 请求流水号req_seq_id
        $reqSeqId = date('YmdHis') . mt_rand(1000, 9999);
        $this->request->setReqSeqId($reqSeqId);
        $this->request->setHuifuId($query['huifu_id']);
        //begin_date
        $this->request->setBeginDate($query['begin_date']);
        //end_date
        $this->request->setEndDate($query['end_date']);
        //page_size
        $this->request->setPageSize($query['page_size']);

        $this->getResult();
        if ($this->request_success && isset($this->result['data'])) {
            return $this->result['data'];
        }

    }

    /**
     * 批量出交易查询
     * v2/trade/batchtranslog/query
     */
    public function batchTranslogQuery($query){
        $this->init('partner');
        $this->request = new V2TradeBatchtranslogQueryRequest();
        $this->request->setHuifuId($query['huifu_id']);

        //begin_date
        $this->request->setBeginDate($query['begin_date']);
        //end_date
        $this->request->setEndDate($query['end_date']);

        $this->getResult();
        if ($this->request_success && isset($this->result['data'])) {
            if ($this->result['data']['result_count']){
                $this->result['data']['trans_log_result_list'] = json_decode($this->result['data']['trans_log_result_list'],true);
                return $this->result['data'];
            }
            return $this->result['data'];
        }

    }


    /**
     * 用户余额查询
     * @param $query
     */
    public function userBalanceQuery($query, $is_total=0){
        $this->init('partner');
        $this->request = new V2TradeAcctpaymentBalanceQueryRequest();
        // 请求日期
        $this->request->setReqDate(date("Ymd"));
        // 请求流水号req_seq_id
//        $reqSeqId = date('YmdHis') . mt_rand(1000, 9999);
//        $this->request->setReqSeqId($reqSeqId);
        $this->request->setHuifuId($query['huifu_id']);
        $this->getResult();
        if ($this->request_success && isset($this->result['data']['acctInfo_list'])) {
            $acctInfo_list = json_decode($this->result['data']['acctInfo_list']);
            if ($is_total == 1 && is_array($acctInfo_list) && $acctInfo_list){
                $total = array_sum(array_column($acctInfo_list, 'avl_bal'));
                return ['total' => $total];
            }
            return $acctInfo_list;
        }
        return [];
    }

    /**
     * 用户余额流水查询
     */

    public function userAcctlogQuery($query){
        $this->init('partner');
        $this->request = new V2TradeAcctpaymentAcctlogQueryRequest();
        // 请求流水号req_seq_id
        $reqSeqId = date('YmdHis') . mt_rand(1000, 9999);
        $this->request->setReqSeqId($reqSeqId);
        $this->request->setHuifuId($query['huifu_id']);
        //账务日期
        $this->request->setAcctDate($query['acct_date']);
        $this->getResult();
        if ($this->request_success && isset($this->result['data'])) {
            if (isset($this->result['data']['acct_log_list'])){
                $this->result['data']['acct_log_list'] = json_decode($this->result['data']['acct_log_list'],true);
            }
            return $this->result['data'];
        }
    }




    /**
     * 供货商企业注册
     */
    public function regShopEnt(){
        $this->init('partner');
    }
}
