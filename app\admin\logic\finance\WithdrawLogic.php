<?php

namespace app\admin\logic\finance;

use app\admin\logic\WechatMerchantTransferLogic;
use app\common\basics\Logic;
use app\common\enum\DistributionOrderGoodsEnum;
use app\common\enum\PayEnum;
use app\common\model\distribution\Distribution;
use app\common\model\distribution\DistributionLevel;
use app\common\model\distribution\DistributionOrderGoods;
use app\common\model\order\Order;
use app\common\model\order\OrderTrade;
use app\common\model\order\OrderTradeProfitSharing;
use app\common\model\RechargeOrder;
use app\common\model\shop\ShopBank;
use app\common\model\user\UserAuth;
use app\common\model\user\UserAuthentication;
use app\common\model\WithdrawApply;
use app\common\enum\WithdrawEnum;
use app\common\server\ConfigServer;
use app\common\server\ExportExcelServer;
use app\common\server\HuifuPayServer;
use app\common\server\UrlServer;
use app\common\model\user\User;
use app\common\logic\AccountLogLogic;
use app\common\model\AccountLog;
use app\admin\logic\WechatCorporatePaymentLogic;
use app\common\server\WeChatProfitSharingServer;
use think\facade\Db;
use think\Exception;

/**
 * Class WithdrawLogic
 * @package app\admin\logic\finance
 */
class WithdrawLogic extends Logic
{
    protected static $error = '';
    /**
     * @notes 会员佣金提现列表
     * @param $get
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/7/14 10:00 上午
     */
    public static function lists($get, $is_export = false)
    {

        $where = [];
        // 会员信息
        if (!empty($get['search_key']) && !empty($get['keyword'])) {
            $keyword = $get['keyword'];
            if ($get['search_key'] == 'user_sn') {
                $where[] = ['u.sn', '=', $keyword];
            } elseif ($get['search_key'] == 'nickname') {
                $where[] = ['u.nickname', 'like', '%' . $keyword . '%'];
            }
        }

        //提现单号
        if (isset($get['withdraw_sn']) && $get['withdraw_sn'] != '') {
            $where[] = ['w.sn', '=', $get['withdraw_sn']];
        }

        //提现方式
        if (isset($get['type']) && $get['type'] != '') {
            $where[] = ['w.type', '=', $get['type']];
        }

        //提现状态
        if (isset($get['status']) && $get['status'] != '') {
            $where[] = ['status', '=', $get['status']];
        }

        if (empty($get['start_time']) && empty($get['end_time'])) {
            $where[] = ['w.create_time', '>=', strtotime(date("Y-m-d", time()))];
            $where[] = ['w.create_time', '<=', strtotime(date("Y-m-d", time())) + 86399];
        }

        // 提现时间
        if (isset($get['start_time']) && $get['start_time'] && isset($get['end_time']) && $get['end_time']) {
            $where[] = ['w.create_time', 'between', [strtotime($get['start_time']), strtotime($get['end_time'])]];
//        }else{
////            $where[] = ['w.create_time', 'between', Time::today()];
        }

        // 导出
        if (true === $is_export) {
            return self::withdrawExport($where);
        }


        $lists = WithdrawApply::alias('w')
            ->field('w.*, u.nickname,u.avatar, u.sn as user_sn, u.mobile, ul.name as user_level_name')
            ->with('user')
            ->leftJoin('user u', 'u.id = w.user_id')
            ->leftJoin('user_level ul', 'ul.id = u.level')
            ->where($where)
            ->page($get['page'], $get['limit'])
            ->order('w.id desc')
            ->select();
        $count = WithdrawApply::alias('w')
            ->field('w.*, u.nickname,u.avatar, u.sn as user_sn, u.mobile, ul.name as user_level_name')
            ->leftJoin('user u', 'u.id = w.user_id')
            ->leftJoin('user_level ul', 'ul.id = u.level')
            ->where($where)
            ->order('w.id desc')
            ->count();


        foreach ($lists as &$item) {
            if (empty($item['user'])) {
                // 用户不存在
                $user = [
                    'avatar' => '',
                    'sn' => '-',
                    'nickname' => '-',
                ];
            } else {
                $user  = $item['user'];
            }
            $item['type_text'] = WithdrawEnum::getTypeDesc($item['type']);
            $item['status_text'] = WithdrawEnum::getStatusDesc($item['status']);
            $item['avatar'] = UrlServer::getFileUrl($item['avatar']);
            $user['avatar'] = UrlServer::getFileUrl($user['avatar']);
            $item['user_level_name'] = $item['user_level_name'] ? $item['user_level_name'] : '无等级';
            $user['user_level_name'] = $item['user_level_name'];
            // 通过中间变量$user解决Indirect modification of overloaded element报错
            $item['user'] = $user;
        }
        return ['count' => $count, 'lists' => $lists];
    }

    /**
     * @notes 数据汇总
     * @return array
     * <AUTHOR>
     * @date 2021/7/14 10:01 上午
     */
    public static function summary()
    {

        $model = new WithdrawApply();
        $successWithdraw = $model->where(['status' => WithdrawEnum::STATUS_SUCCESS])->sum('money');
        $handleWithdraw = $model->where(['status' => WithdrawEnum::STATUS_ING])->sum('money');
        $totalEarnings = (new User())->where(['del' => 0])->sum('earnings');

        return ['successWithdraw' => $successWithdraw, 'handleWithdraw' => $handleWithdraw, 'totalEarnings' => $totalEarnings];
    }

    /**
     * @notes 佣金明细
     * @param $get
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/7/14 10:01 上午
     */
    public static function commission($get, $is_export = false)
    {

        $where = [];

        // 明细类型
        $source_type = AccountLog::earnings_change;
        if (isset($get['source_type']) && !empty($get['source_type'])) {
            $where[] = ['a.source_type', '=', $get['source_type']];
        } else {
            $where[] = ['a.source_type', 'in', $source_type];
        }

        //明细搜索
        if (!empty($get['search_key']) && !empty($get['keyword'])) {
            $keyword = $get['keyword'];
            switch ($get['search_key']) {
                case  'user_sn' :
                    $where[] = ['u.sn', '=', $keyword];
                    break;
                case  'nickname' :
                    $where[] = ['u.nickname', 'like', '%' . $keyword . '%'];
                    break;
            }
        }

        if (empty($get['start_time']) && empty($get['end_time'])) {
            $where[] = ['a.create_time', '>=', strtotime(date("Y-m-d", time()))];
            $where[] = ['a.create_time', '<=', strtotime(date("Y-m-d", time())) + 86399];
        }

        //明细时间
        if (isset($get['start_time']) && $get['start_time'] != '') {
            $where[] = ['a.create_time', '>=', strtotime($get['start_time'])];
        }

        if (isset($get['end_time']) && $get['end_time'] != '') {
            $where[] = ['a.create_time', '<=', strtotime($get['end_time'])];
        }

        // 导出
        if (true === $is_export) {
            return self::commissionExport($where);
        }

        $lists = AccountLog::alias('a')
            ->field('a.*,u.nickname,u.sn as user_sn,u.mobile,w.sn as withdraw_sn')
            ->join('user u', 'u.id = a.user_id')
            ->leftjoin('withdraw_apply w', 'w.sn = a.source_sn')
            ->where($where)
            ->page($get['page'], $get['limit'])
            ->order('a.id desc')
            ->select();

        $count = AccountLog::alias('a')
            ->field('a.*,u.nickname,u.sn as user_sn,u.mobile,w.sn as withdraw_sn')
            ->join('user u', 'u.id = a.user_id')
            ->leftjoin('withdraw_apply w', 'w.sn = a.source_sn')
            ->where($where)
            ->order('a.id desc')
            ->count();

        return ['count' => $count, 'lists' => $lists];
    }

    /**
     * @notes 账户明细
     * @param $get
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/7/14 10:01 上午
     */
    public static function account($get, $is_export = false)
    {

        $where = [];

        // 明细类型
        $source_type = AccountLog::money_change;
        if (isset($get['type']) && !empty($get['type'])) {
            switch ($get['type']) {
                case 'admin_add_money' :
                    $type = AccountLog::admin_add_money;
                    break;
                case 'admin_reduce_money' :
                    $type = AccountLog::admin_reduce_money;
                    break;
                case 'recharge_money' :
                    $type = AccountLog::recharge_money;
                    break;
                case 'balance_pay_order' :
                    $type = AccountLog::balance_pay_order;
                    break;
                case 'cancel_order_refund' :
                    $type = AccountLog::cancel_order_refund;
                    break;
                case 'after_sale_refund' :
                    $type = AccountLog::after_sale_refund;
                    break;
                case 'withdraw_to_balance' :
                    $type = AccountLog::withdraw_to_balance;
                    break;
                case 'user_transfer_inc_balance' :
                    $type = AccountLog::user_transfer_inc_balance;
                    break;
                case 'user_transfer_dec_balance' :
                    $type = AccountLog::user_transfer_dec_balance;
                    break;
                case 'integral_order_inc_balance' :
                    $type = AccountLog::integral_order_inc_balance;
                    break;
                case 'integral_order_dec_balance' :
                    $type = AccountLog::integral_order_dec_balance;
                    break;
            }
            $where[] = ['a.source_type', '=', $type];
        } else {
            $where[] = ['a.source_type', 'in', $source_type];
        }

        //明细搜索
        if (!empty($get['search_key']) && !empty($get['keyword'])) {
            $keyword = $get['keyword'];
            switch ($get['search_key']) {
                case  'user_sn' :
                    $where[] = ['u.sn', '=', $keyword];
                    break;
                case  'nickname' :
                    $where[] = ['u.nickname', 'like', '%' . $keyword . '%'];
                    break;
            }
        }

        if (empty($get['start_time']) && empty($get['end_time'])) {
            $where[] = ['a.create_time', '>=', strtotime(date("Y-m-d", time()))];
            $where[] = ['a.create_time', '<=', strtotime(date("Y-m-d", time())) + 86399];
        }

        //明细时间
        if (isset($get['start_time']) && $get['start_time'] != '') {
            $where[] = ['a.create_time', '>=', strtotime($get['start_time'])];
        }

        if (isset($get['end_time']) && $get['end_time'] != '') {
            $where[] = ['a.create_time', '<=', strtotime($get['end_time'])];
        }

        // 导出
        if (true === $is_export) {
            return self::accountExport($where);
        }

        $lists = AccountLog::alias('a')
            ->field('a.*,u.nickname,u.sn as user_sn,u.mobile')
            ->join('user u', 'u.id = a.user_id')
            ->where($where)
            ->page($get['page'], $get['limit'])
            ->order('a.id desc')
            ->select();

        $count = AccountLog::alias('a')
            ->field('a.*,u.nickname,u.sn as user_sn,u.mobile')
            ->join('user u', 'u.id = a.user_id')
            ->where($where)
            ->order('a.id desc')
            ->count();

        return ['count' => $count, 'lists' => $lists];
    }

    /**
     * @notes 充值明细
     * @param $get
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/7/14 10:01 上午
     */
    public static function recharge($get, $is_export = false)
    {

        $where = [];

        //明细搜索
        if (isset($get['search_key']) && !empty($get['search_key'])) {
            $keyword = $get['keyword'];
            switch ($get['search_key']) {
                case  'nickname' :
                    $where[] = ['u.nickname', 'like', '%' . $keyword . '%'];
                    break;
                case  'order_sn' :
                    $where[] = ['order_sn', '=', $keyword];
                    break;
                case  'user_mobile' :
                    $where[] = ['u.mobile', '=', $keyword];
                    break;
            }
        }

        //订单来源
        if (isset($get['order_source']) && $get['order_source'] != '') {
            $where[] = ['r.order_source', '=', $get['order_source']];
        }

        //订单状态
        if (isset($get['pay_status']) && $get['pay_status'] != '') {
            $where[] = ['r.pay_status', '=', $get['pay_status']];
        }

        //支付方式
        if (isset($get['pay_way']) && $get['pay_way'] != '') {
            $where[] = ['r.pay_way', '=', $get['pay_way']];
        }

        if (empty($get['start_time']) && empty($get['end_time'])) {
            $where[] = ['r.create_time', '>=', strtotime(date("Y-m-d", time()))];
            $where[] = ['r.create_time', '<=', strtotime(date("Y-m-d", time())) + 86399];
        }

        //明细开始时间
        if (isset($get['start_time']) && $get['start_time'] != '') {
            $where[] = ['r.create_time', '>=', strtotime($get['start_time'])];
        }
        //明细结束时间
        if (isset($get['end_time']) && $get['end_time'] != '') {
            $where[] = ['r.create_time', '<=', strtotime($get['end_time'])];
        }

        // 导出
        if (true === $is_export) {
            return self::rechargeExport($where);
        }

        $lists = RechargeOrder::alias('r')
            ->field('r.*,u.id,u.nickname,u.mobile')
            ->join('user u', 'u.id = r.user_id')
            ->where($where)
            ->page($get['page'], $get['limit'])
            ->order('r.id desc')
            ->select();
        foreach ($lists as $list) {
            if (!empty($list['pay_time'])) {
                $list['pay_time'] = date('Y-m-d H:i:s', $list['pay_time']);
            }
        }

        $count = RechargeOrder::alias('r')
            ->field('r.*,u.id,u.nickname,u.mobile')
            ->join('user u', 'u.id = r.user_id')
            ->where($where)
            ->order('r.id desc')
            ->count();

        return ['count' => $count, 'lists' => $lists];
    }

    /**
     * @notes 会员佣金提现详情
     * @param $id
     * @return array|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/7/14 10:01 上午
     */
    public static function detail($id)
    {

        $detail = WithdrawApply::alias('w')
            ->field('w.*,u.sn as user_sn, u.nickname, u.mobile')
            ->leftJoin('user u', 'u.id=w.user_id')
            ->where('w.id', $id)
            ->find();
        $detail['typeDesc'] = WithdrawEnum::getTypeDesc($detail['type']);
        $detail['statusDesc'] = WithdrawEnum::getStatusDesc($detail['status']);
        $detail['money_qr_code'] = UrlServer::getFileUrl($detail['money_qr_code']);
        $detail['id_front'] = UrlServer::getFileUrl($detail['id_front']);
        $detail['id_back'] = UrlServer::getFileUrl($detail['id_back']);
        $detail['transfer_time'] = $detail['transfer_time'] ? date('Y-m-d H:i:s', $detail['transfer_time']) : '';
        $detail['payment_time'] = $detail['payment_time'] ? date('Y-m-d H:i:s', $detail['payment_time']) : '';
        return $detail;
    }

    /**
     * Notes: 提现单关联分佣单列表
     * Author: Darren
     * DateTime: 2023-07-15 10:26
     * @param $id
     */
    public static function withdraw_order_list($id){
        $where= [];
        // 订单信息
        $where[] = ['dog.draw_id', '=', $id];

        $field = [
            'o.id' => 'order_id',
            'o.order_sn',
            'o.transaction_id',
            'o.pay_time' => 'order_pay_time',
            'o.pay_way',
            'o.user_id' => 'order_user_id',
            'u.id' => 'distribution_user_id',
            'u.avatar' => 'distribution_avatar',
            'u.mobile' => 'mobile',
            'u.sn' => 'distribution_sn',
            'u.nickname' => 'distribution_nickname',
            'og.image' => 'goods_image',
            'og.goods_name' => 'goods_name',
            'og.spec_value' => 'spec_value',
            'og.goods_num' => 'goods_num',
            'og.total_pay_price' => 'total_pay_price',
            'dog.level_id',
            'dog.level',
            'dog.ratio',
            'dog.money',
            'dog.user_id',
            'dog.status' => 'status_desc',
            'dog.settlement_time'
        ];

        $lists = DistributionOrderGoods::alias('dog')
            ->leftJoin('order o', 'o.id = dog.order_id')
            ->leftJoin('user u', 'u.id = dog.user_id')
            ->leftJoin('order_goods og', 'og.id = dog.order_goods_id')
            ->field($field)
            ->where($where)
            ->order('dog.id', 'desc')
            ->select()
            ->toArray();

        $total_money = $total_pay_price = $goods_num = 0;
        foreach($lists as &$item) {
            $item['order_pay_time'] = date('Y-m-d H:i:s', $item['order_pay_time']);
            $item['user_info'] = User::getUserInfo($item['order_user_id']);
            if ($item['user_info'] == '系统') {
                // 用户不存在(已被删除的情况)
                $item['user_info'] = [
                    'avatar' => '',
                    'nickname' => '-',
                    'sn' => '-',
                ];
            }
            $item['pay_way'] = PayEnum::getPayWay($item['pay_way']);
            $total_money += $item['money'];
            $total_pay_price += $item['total_pay_price'];
            $goods_num += $item['goods_num'];
//            $item['goods_image'] = empty($item['goods_image']) ? '' : UrlServer::getFileUrl($item['goods_image']);
        }

        return [
            'total' => [
                'total_money'=>money($total_money),
                'total_pay_price'=>money($total_pay_price),
                'goods_num'=>$goods_num
            ],
            'count'=>count($lists),
            'lists' => $lists
        ];


//
//
//
//
//
//
//        $list = DistributionOrderGoods::where('draw_id', $id)->column('user_id,order_id,order_goods_id,create_time,status,money,total_price,buyer_user_id,settlement_time');
//        if (!$list){
//            return [];
//        }
//        return ['data'=>$list, 'count'=>1];
    }

    /**
     * @notes 审核通过
     * @param $post
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     * <AUTHOR>
     * @date 2021/7/14 10:02 上午
     */
    public static function confirm($post)
    {

        $id = $post['id'];
        $withdraw = WithdrawApply::where('id', $id)
            ->find();

        // 判断提现单是否为待提现状态 1
        if ($withdraw['status'] != 1) {
            return [
                'code' => 0,
                'msg' => '不是待提现申请单'
            ];
        }

        //提现到钱包余额
        if ($withdraw['type'] == WithdrawEnum::TYPE_BALANCE) {
            $user = User::find($withdraw['user_id']);
            $user->user_money = ['inc', $withdraw['left_money']];
            $user->save();
            AccountLogLogic::AccountRecord(
                $withdraw['user_id'],
                $withdraw['left_money'],
                1,
                AccountLog::withdraw_to_balance,
                '',
                $withdraw['id'],
                $withdraw['sn']
            );
            //更新提现申请单状态为提现成功
            WithdrawApply::where('id', $id)
                ->update(['status' => WithdrawEnum::STATUS_SUCCESS, 'update_time' => time(), 'description' => $post['description']]);

            return [
                'code' => 1,
                'msg' => '提现至钱包余额成功'
            ];
        }
        //提现到微信零钱
        if ($withdraw['type'] == WithdrawEnum::TYPE_WECHAT_CHANGE) {
            // 先更新审核备注
            WithdrawApply::where('id', $id)
                ->update(['update_time' => time(), 'description' => $post['description']]);

            //微信零钱接口:1-企业付款到零钱;2-商家转账到零钱
            $transfer_way = ConfigServer::get('withdraw', 'transfer_way',1);
            if ($transfer_way == 1) {
                return WechatCorporatePaymentLogic::pay($withdraw);
            }
            if ($transfer_way == 2) {
                return WechatMerchantTransferLogic::transfer($withdraw);
            }
            //分账到零钱
            if ($transfer_way == 3) {
                return self::withdrawProfitSharing($withdraw);
            }
        }

        //提现到微信收款码、支付收款码
        if ($withdraw['type'] == WithdrawEnum::TYPE_WECHAT_CODE || $withdraw['type'] == WithdrawEnum::TYPE_ALI_CODE || WithdrawEnum::TYPE_BANK) {
            // 直接标识为提现中状态
            WithdrawApply::where('id', $id)
                ->update(['status' => WithdrawEnum::STATUS_ING, 'update_time' => time(), 'description' => $post['description']]);
            return [
                'code' => 1,
                'msg' => '审核通过，提现中'
            ];
        }
    }

    /**
     * @notes 审核拒绝
     * @param $post
     * @throws \think\exception\PDOException
     * <AUTHOR>
     * @date 2021/7/14 10:03 上午
     */
    public static function refuse($post)
    {

        $time = time();
        Db::startTrans();
        try {
            $withdraw_apply = WithdrawApply::where('id', $post['id'])->where('status', '=', WithdrawEnum::STATUS_WAIT)->find();
            if (!$withdraw_apply){
                throw new Exception('提现申请已经处理或者不存在');
            }
            $withdraw_apply->status = WithdrawEnum::STATUS_FAIL; // 提现失败
            $withdraw_apply->description = $post['description'];
            $withdraw_apply->update_time = $time;
            $withdraw_apply->save();

            //拒绝提现,回退佣金
            $user = User::find($withdraw_apply['user_id']);
            if ($withdraw_apply['type'] == WithdrawEnum::TYPE_HUIFU_BANK){
                $user->partner_earnings = ['inc', $withdraw_apply['money']];
            }else{
                $user->earnings = ['inc', $withdraw_apply['money']];
            }
            $user->save();

            $where_dis=[
                'user_id'=>$withdraw_apply['user_id'],
                'draw_id'=>$post['id'],
                'status'=>DistributionOrderGoodsEnum::STATUS_WITHDRAW_ING
            ];
            $update=['status'=>DistributionOrderGoodsEnum::STATUS_SUCCESS, 'update_time'=>$time];
            DistributionOrderGoods::update($update, $where_dis);
            //增加佣金变动记录
            AccountLogLogic::AccountRecord(
                $withdraw_apply['user_id'],
                $withdraw_apply['money'],
                1,
                AccountLog::withdraw_back_earnings,
                '',
                $withdraw_apply['id'],
                $withdraw_apply['sn']
            );
            Db::commit();
            return true;
        } catch (Exception $e) {
            Db::rollback();
            return $e->getMessage();
        }
    }

    /**
     * @notes 审核拒绝
     * @param $post
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     * <AUTHOR>
     * @date 2021/7/14 10:03 上午
     */
    public static function transferFail($post)
    {

        if (empty($post['transfer_voucher'])) {
            return [
                'code' => 0,
                'msg' => '请上传错误截图'
            ];
        }
        if (empty($post['transfer_description'])) {
            return [
                'code' => 0,
                'msg' => '请填写转账说明'
            ];
        }
        $withdraw_apply = WithdrawApply::where('id', $post['id'])->find();
        if (!$withdraw_apply || $withdraw_apply['status'] != WithdrawEnum::STATUS_ING){
            return [
                'code' => 0,
                'msg' => '申请提现不存在或者状态错误'
            ];
        }
        $time = time();

        Db::startTrans();
        try {
            // 标识提现失败
            WithdrawApply::where('id', $post['id'])->update([
                'status' => 4, // 提现失败
                'transfer_voucher' => $post['transfer_voucher'] ? $post['transfer_voucher'] : '',
                'transfer_description' => $post['transfer_description'],
                'update_time' => $time
            ]);


            // 退回佣金
            $user = User::find($withdraw_apply['user_id']);
            $user->earnings = ['inc', $withdraw_apply['money']];
            $user->save();

            //还原申请提现的记录
            $where_dis=[
                'user_id'=>$withdraw_apply['user_id'],
                'draw_id'=>$post['id'],
                'status'=>DistributionOrderGoodsEnum::STATUS_WITHDRAW_ING
            ];
            $update=['status'=>DistributionOrderGoodsEnum::STATUS_SUCCESS, 'update_time'=>$time];
            DistributionOrderGoods::update($update, $where_dis);

            //增加佣金变动记录
            AccountLogLogic::AccountRecord(
                $withdraw_apply['user_id'],
                $withdraw_apply['money'],
                1,
                AccountLog::withdraw_back_earnings,
                '',
                $withdraw_apply['id'],
                $withdraw_apply['sn']
            );
            Db::commit();
            return [
                'code' => 1,
                'msg' => '提交保存成功, 提现金额已退回佣金账户'
            ];
        } catch (Exception $e) {
            Db::rollback();
            return [
                'code' => 0,
                'msg' => $e->getMessage() ?: '提交保存失败'
            ];
        }
    }

    /**
     * @notes 转账成功
     * @param $post
     * @return array
     * <AUTHOR>
     * @date 2021/7/14 10:03 上午
     */
    public static function transferSuccess($post)
    {

        if (empty($post['transfer_voucher'])) {
            return [
                'code' => 0,
                'msg' => '请上传转账凭证'
            ];
        }

        $post['transfer_voucher'] = UrlServer::getFileUrl($post['transfer_voucher']);

        if (empty($post['transfer_description'])) {
            return [
                'code' => 0,
                'msg' => '请填写转账说明'
            ];
        }
        $time = time();
        //更新所有提现中记录为已提现
        $apply = WithdrawApply::where(['id'=> $post['id'], 'status'=>2])->find()->toArray();
        if (!$apply){
            return [
                'code' => 0,
                'msg' => '记录不存在或者已经修改'
            ];
        }
        $where_dis=[
            'user_id'=>$apply['user_id'],
            'draw_id'=>$post['id'],
            'status'=>DistributionOrderGoodsEnum::STATUS_WITHDRAW_ING
        ];
        $update=['status'=>DistributionOrderGoodsEnum::STATUS_WITHDRAW, 'update_time'=>$time];
        DistributionOrderGoods::update($update, $where_dis);
        // 标识提现成功
        WithdrawApply::where('id', $post['id'])->update([
            'status' => 3, // 提现成功
            'transfer_voucher' => $post['transfer_voucher'],
            'transfer_description' => $post['transfer_description'],
            'update_time' => $time,
            'transfer_time' => $time
        ]);

        return [
            'code' => 1,
            'msg' => '转账成功'
        ];
    }

    /**
     * @notes 提现失败
     * @param $id
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     * <AUTHOR>
     * @date 2021/7/14 10:03 上午
     */
    public static function withdrawFailed($id)
    {

        $withdraw_apply = WithdrawApply::where('id', $id)->find();
        $withdraw_apply->status = WithdrawEnum::STATUS_FAIL; // 提现失败
        $withdraw_apply->update_time = time();
        $withdraw_apply->save();

        //拒绝提现,回退佣金
        $user = User::find($withdraw_apply['user_id']);
        $user->earnings = ['inc', $withdraw_apply['money']];
        $user->save();
        DistributionOrderGoods::update(['status'=>DistributionOrderGoodsEnum::STATUS_SUCCESS, 'update_time'=>time(), 'draw_id'=>$withdraw_apply['id']], ['draw_id'=>$withdraw_apply['id']]);
        //增加佣金变动记录
        AccountLogLogic::AccountRecord(
            $withdraw_apply['user_id'],
            $withdraw_apply['money'],
            1,
            AccountLog::withdraw_back_earnings,
            '',
            $withdraw_apply['id'],
            $withdraw_apply['sn']
        );
    }

    /**
     * @notes 发送提现申请
     * @param $id
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function sendHuifu($id)
    {
        // 1. 查询提现单
        $withdraw = WithdrawApply::where('id', $id)->find();
        if (!$withdraw) {
            return ['code' => 0, 'msg' => '提现申请不存在'];
        }

        // 2. 校验提现单状态
        if ($withdraw['status'] != WithdrawEnum::STATUS_ING) {
            return ['code' => 0, 'msg' => '提现单状态不正确'];
        }

        // 3. 校验提现方式
        if ($withdraw['type'] != WithdrawEnum::TYPE_HUIFU_BANK) {
            return ['code' => 0, 'msg' => '提现方式不是汇付'];
        }

        // 4. 校验用户及认证
        $user = User::find($withdraw['user_id']);
        if (!$user) {
            return ['code' => 0, 'msg' => '用户不存在'];
        }
        $user_auth = UserAuthentication::where('user_id', $user['id'])->where('auth_status', 10)->find();
        if (!$user_auth) {
            return ['code' => 0, 'msg' => '用户未实名认证'];
        }

        // 5. 校验提现金额、银行卡等
        if ($withdraw['money'] <= 0) {
            return ['code' => 0, 'msg' => '提现金额异常'];
        }
        // ...银行卡等校验...

        // 6. 防重复
        if ($withdraw['huifu_status'] == 1) {
            return ['code' => 0, 'msg' => '已发起汇付请求，请勿重复操作'];
        }

        // 7. 组织参数
        $huifu_data = [
            'huifu_id' => $withdraw['id'],
            'cash_amt' => $withdraw['money'],
            'token_no' => $withdraw['sn'],
            // 其他必要参数
        ];

        // 8. 请求汇付
        try {
            $huifuServer = HuifuPayServer::getInstance();
            $result = $huifuServer->userEncashment($huifu_data);

            if ($result['code'] == 0) {
                // 成功
                WithdrawApply::where('id', $id)->update([
                    'status' => WithdrawEnum::STATUS_SUCCESS,
                    'huifu_status' => 1,
                    'update_time' => time(),
                    // 其他字段
                ]);
                return ['code' => 1, 'msg' => '提现成功'];
            } else {
                // 失败
                WithdrawApply::where('id', $id)->update([
                    'status' => WithdrawEnum::STATUS_FAIL,
                    'huifu_status' => 2,
                    'update_time' => time(),
                    'description' => $result['message'],
                ]);
                // 回退佣金
                // ...回退逻辑...
                return ['code' => 0, 'msg' => '提现失败：' . $result['message']];
            }
        } catch (\Exception $e) {
            // 异常处理
            return ['code' => 0, 'msg' => '系统异常：' . $e->getMessage()];
        }
    }

    /**
     * @notes 搜索
     * @param $id
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/7/14 10:03 上午
     */
    public static function search($id)
    {

        $withdraw = WithdrawApply::where('id', $id)
            ->find();

        // 判断提现单是否为提现中状态 2 且 提现方式为 微信零钱 2
        if ($withdraw['status'] == 2 && $withdraw['type'] == 2) {
            //微信零钱接口:1-企业付款到零钱;2-商家转账到零钱
            $transfer_way = ConfigServer::get('withdraw', 'transfer_way',1);
            if ($transfer_way == 1) {
                return WechatCorporatePaymentLogic::search($withdraw);
            }
            if ($transfer_way == 2) {
                $result = WechatMerchantTransferLogic::details($withdraw);
                // 记录查询结果
                WithdrawApply::update(['update_time'=>time(),'pay_search_desc'=>json_encode($result, JSON_UNESCAPED_UNICODE)],['id'=>$withdraw['id']]);
                if(isset($result['detail_status'])) {
                    if ($result['detail_status'] == 'SUCCESS') {
                        // 转账成功,标记提现申请单为提现成功,记录支付信息
                        WithdrawApply::update(['status'=>3,'payment_no'=>$result['detail_id'],'payment_time'=>strtotime($result['update_time'])],['id'=>$withdraw['id']]);
                        return ['code' => 1, 'msg' => '提现成功'];
                    }
                    if ($result['detail_status'] == 'FAIL') {
                        // 转账失败
                        WithdrawApply::update(['status'=>4],['id'=>$withdraw['id']]);
                        //回退佣金
                        $user = User::find($withdraw['user_id']);
                        $user->earnings = ['inc', $withdraw['money']];
                        $user->save();

                        //增加佣金变动记录
                        AccountLogLogic::AccountRecord(
                            $withdraw['user_id'],
                            $withdraw['money'],
                            1,
                            AccountLog::withdraw_back_earnings,
                            '',
                            $withdraw['id'],
                            $withdraw['sn']
                        );
                        return ['code' => 1, 'msg' => '提现至微信零钱失败'];
                    }
                    if ($result['detail_status'] == 'PROCESSING') {
                        return ['code' => 0, 'msg' => '正在处理中'];
                    }
                }else{
                    return ['code' => 0, 'msg' => $result['message'] ?? '商家转账到零钱查询失败'];
                }
            }
        } else {
            return [
                'code' => 0,
                'msg' => '不是提现中的微信零钱申请单，无法查询'
            ];
        }
    }



    /**
     * @notes 导出Excel
     * @param array $where
     * @return array|false
     * <AUTHOR>
     * @date 2022/4/24 10:10
     */
    public static function rechargeExport($where)
    {
        try {
            $lists = RechargeOrder::alias('r')
                ->field('r.*,u.id,u.nickname,u.mobile')
                ->join('user u', 'u.id = r.user_id')
                ->where($where)
                ->order('r.id desc')
                ->select()->toArray();

            foreach ($lists as &$list) {
                if (!empty($list['pay_time'])) {
                    $list['pay_time'] = date('Y-m-d H:i:s', $list['pay_time']);
                }
            }

            $excelFields = [
                'order_sn' => '订单编号',
                'nickname' => '用户昵称',
                'mobile' => '用户手机号',
                'order_amount' => '充值金额',
                'give_money' => '赠送金额',
                'give_growth' => '赠送成长值',
                'pay_way' => '支付方式',
                'pay_time' => '支付时间',
                'pay_status' => '订单状态',
                'create_time' => '下单时间',
            ];

            $export = new ExportExcelServer();
            $export->setFileName('充值明细');
            $result = $export->createExcel($excelFields, $lists);

            return ['url' => $result];

        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }


    /**
     * @notes 导出Excel
     * @param array $where
     * @return array|false
     * <AUTHOR>
     * @date 2022/4/24 10:10
     */
    public static function accountExport($where)
    {
        try {
            $lists = AccountLog::alias('a')
                ->field('a.*,u.nickname,u.sn as user_sn,u.mobile')
                ->join('user u', 'u.id = a.user_id')
                ->where($where)
                ->order('a.id desc')
                ->select();

            $excelFields = [
                'nickname' => '会员昵称',
                'user_sn' => '会员编号',
                'mobile' => '手机号码',
                'change_amount' => '变动金额',
                'left_amount' => '剩余金额',
                'source_type' => '明细类型',
                'source_sn' => '来源单号',
                'create_time' => '记录时间',
            ];

            $export = new ExportExcelServer();
            $export->setFileName('账户明细');
            $result = $export->createExcel($excelFields, $lists);

            return ['url' => $result];

        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }


    /**
     * @notes 导出Excel
     * @param array $condition
     * @return array|false
     * <AUTHOR>
     * @date 2022/4/24 10:10
     */
    public static function commissionExport($where)
    {
        try {
            $lists = AccountLog::alias('a')
                ->field('a.*,u.nickname,u.sn as user_sn,u.mobile,w.sn as withdraw_sn')
                ->join('user u', 'u.id = a.user_id')
                ->leftjoin('withdraw_apply w', 'w.sn = a.source_sn')
                ->where($where)
                ->order('a.id desc')
                ->select();

            $excelFields = [
                'nickname' => '会员昵称',
                'user_sn' => '会员编号',
                'mobile' => '手机号码',
                'change_amount' => '变动金额',
                'left_amount' => '剩余佣金',
                'source_type' => '明细类型',
                'withdraw_sn' => '来源单号',
                'create_time' => '记录时间',
            ];

            $export = new ExportExcelServer();
            $export->setFileName('佣金明细');
            $result = $export->createExcel($excelFields, $lists);

            return ['url' => $result];

        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }


    /**
     * @notes 导出Excel
     * @param array $condition
     * @return array|false
     * <AUTHOR>
     * @date 2022/4/24 10:10
     */
    public static function withdrawExport($where)
    {
        try {
            $lists = WithdrawApply::alias('w')
                ->field('w.*, u.nickname,u.avatar, u.sn as user_sn, u.mobile, ul.name as user_level_name')
                ->with('user')
                ->leftJoin('user u', 'u.id = w.user_id')
                ->leftJoin('user_level ul', 'ul.id = u.level')
                ->where($where)
                ->order('w.id desc')
                ->select();

            foreach ($lists as &$item) {
                $item['type_text'] = WithdrawEnum::getTypeDesc($item['type']);
                $item['status_text'] = WithdrawEnum::getStatusDesc($item['status']);
            }

            $excelFields = [
                'sn' => '提现单号',
                'nickname' => '会员昵称',
                'user_sn' => '会员编号',
                'mobile' => '手机号码',
                'left_money' => '提现金额',
                'type_text' => '提现方式',
                'status_text' => '提现状态',
                'remark' => '提现说明',
                'create_time' => '提现时间',
            ];

            $export = new ExportExcelServer();
            $export->setFileName('佣金提现');
            $result = $export->createExcel($excelFields, $lists);

            return ['url' => $result];

        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * Notes: 个人 提现单通过微信分账方式分账到零钱
     * Author: Darren
     * DateTime: 2023-08-26 11:20
     */
    public static function withdrawProfitSharing($withdraw){
        $result = [
            'code'=> 1,
            'msg' => '微信分账到零钱成功'
        ];
        $time = time();
        $user_info = UserAuth::where([['user_id', '=', $withdraw['user_id']]])->field('openid,profit_sharing_status,user_id')->find()->toArray();
        if (!$user_info){
            $result = [
                'code'=> 0,
                'msg' => '未获取用户信息'
            ];
            return $result;
        }
        $user_info['real_name'] = Distribution::where([['user_id', '=', $withdraw['user_id']]])->value('real_name');
        if ($user_info['profit_sharing_status'] != 1){
            $res = WeChatProfitSharingServer::addReceiver($user_info);
            if ($res === true){
                UserAuth::where([['user_id', '=', $withdraw['user_id']]])->update(['profit_sharing_status'=>1, 'update_time'=>time()]);
            }else{
                $result = [
                    'code'=> 0,
                    'msg' => '添加分账用户信息错误'
                ];
                return $result;
            }
        }
        $where_order=[
            'draw_id'=>$withdraw['id'],
            'status'=>DistributionOrderGoodsEnum::STATUS_WITHDRAW_ING
        ];
        $dis_order_list = DistributionOrderGoods::where($where_order)->column('order_id,money,id', 'id');
        $log_list = self::formatOrderList($dis_order_list, $user_info, $withdraw['user_id']);
        if (empty($log_list)){
            $result = [
                'code'=> 0,
                'msg' => self::$error
            ];
            return $result;
        }
        //开始分账
        try {
            $sModel = new OrderTradeProfitSharing();
            $log_list_count = count($log_list);
            $log_success_count =0;
            foreach ($log_list as $log){
                if (!isset($log['result']) || $log['result'] !== 'success'){
                    $res = WeChatProfitSharingServer::multiShare($log['transaction_id'], $log['request_no'], $log['content']);
                    $log['content'] = json_encode($log['content']);
                    if ($res === true){
                        $log['result'] = 'success';
                        if (isset($log['id'])){
                            OrderTradeProfitSharing::update($log, ['id'=>$log['id']]);
                        }else{
                            $log['id'] = $sModel->insertGetId($log);
                        }
                    }else{
                        if (isset($log['id'])){
                            OrderTradeProfitSharing::where('id', $log['id'])->inc('error_count')->update();
                            OrderTradeProfitSharing::update($log, ['id'=>$log['id']]);
                        }else{
                            $log['error_count'] =1;
                            $log['id'] = $sModel->insertGetId($log);
                        }
                        continue;
                    }
                }
                if (isset($log['result']) && $log['result'] == 'success'){
                    $log_success_count++;
                }
            }
            //所有都分账成功后, 修改提现成功
            if ($log_success_count == $log_list_count){
                $update_apply=[
                    'status'=>WithdrawApply::STATUS_SUCCESS,
                    'transfer_description'=>'微信分账成功',
                    'transfer_time'=>$time,
                    'transfer_voucher'=>''  //凭证
                ];
                WithdrawApply::where('id', $withdraw['id'])->update($update_apply);
                return $result;
            }else{
                $update_apply=[
                    'status'=>WithdrawApply::STATUS_ING,
                    'transfer_description'=>'微信分账中',
                    'transfer_time'=>$time
                ];
                WithdrawApply::where('id', $withdraw['id'])->update($update_apply);
                $result = [
                    'code'=> 0,
                    'msg' => '微信分账中'
                ];
                return $result;
            }
        } catch (\Exception $e) {
            $result = [
                'code'=> 0,
                'msg' => '分账错误:'.$e->getMessage()
            ];
            return $result;
        }

    }


    /**
     * Notes: 格式化发送分账的订单列表
     * Author: Darren
     * DateTime: 2023-08-29 10:54
     * @param $order_list
     */
    public static function formatOrderList($dis_order_list, $user_info, $user_id){
        //计算各个订单分佣金额
        foreach ($dis_order_list as $value){
            if (isset($total_order[$value['order_id']])){
                $total_order[$value['order_id']] += $value['money'];
            }else{
                $total_order[$value['order_id']] = $value['money'];
            }
        }
        $order_id_arr = array_column($dis_order_list, 'order_id');
        $order_list = Order::where([['id', 'in', $order_id_arr]])->column('id,trade_id,pay_way,order_sn', 'id');
        foreach ($order_list as $value){
            if ($value['pay_way'] != 1){
                self::$error = '订单$order_id:'.$value['id'].'不是微信支付';
                return false;
            }
        }
        $trade_id_arr = array_column($order_list, 'trade_id');
        $order_trade_list = OrderTrade::where([['id', 'in', $trade_id_arr]])->column('transaction_id', 'id');
        foreach ($order_list as &$value){
            $value['transaction_id'] = $order_trade_list[$value['trade_id']];
            $value['money'] = $total_order[$value['id']];
        }
        $where_log=[
            ['trade_id', 'in', $trade_id_arr],
            ['sharing_type', '=',1],
            ['user_id', '=', $user_id]
        ];
        $old_log_lists = OrderTradeProfitSharing::where($where_log)->column('error_count,order_id,result,id,trade_id,request_no', 'order_id');
//        if ($log_list){//手动操作部验证错误次数
//            foreach ($log_list as $value){
//                if ($value['error_count'] > 3){
//                    self::$error = '订单order_id:'.$value['order_id'].'错误次数'.$value['error_count'];
//                    return false;
//                }
//            }
//        }
        $time = time();
        $log_list=[];
        foreach ($order_list as $order_id=>$order){
            if (isset($old_log_lists[$order_id])){
                $log_list[$order_id]['result'] = $old_log_lists[$order_id]['result'];
                $log_list[$order_id]['update_time'] = $time;
                $log_list[$order_id]['id'] = $old_log_lists[$order_id]['id'];
                $log_list[$order_id]['request_no'] = $old_log_lists[$order_id]['request_no'];
            }else{
                $log_list[$order_id]['create_time'] = $time;
                $log_list[$order_id]['request_no'] = createSn('order_trade_profit_sharing', 'request_no', '', 4);
            }
            if (!$order['transaction_id']){
                self::$error = '订单$order_id:'.$order_id.'未获取外部支付单号';
                return false;
            }
            $log_list[$order_id]['transaction_id'] = $order['transaction_id'];
            $log_list[$order_id]['order_id'] = $order_id;
            $log_list[$order_id]['sharing_type'] =2;
            $log_list[$order_id]['trade_id'] = $order['trade_id'];
            $receiver[0]=[
                'type'=>'PERSONAL_OPENID',
                'account'=>$user_info['openid'],
                'name'=> $user_info['real_name'],
                'amount'=>intval($order['money']*100),//结算单位为分
                'description'=>'订单分账,订单编号:'.$order['order_sn']
            ];
            $log_list[$order_id]['content']= $receiver;
        }
        return $log_list;
    }

}