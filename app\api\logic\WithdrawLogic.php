<?php

namespace app\api\logic;

use app\common\basics\Logic;
use app\common\enum\DistributionOrderGoodsEnum;
use app\common\enum\PayEnum;
use app\common\logic\AccountLogLogic;
use app\common\model\{AccountLog, distribution\DistributionOrderGoods, order\Order, Withdraw, WithdrawApply};
use app\common\model\user\User;
use app\common\enum\WithdrawEnum;
use app\common\server\ConfigServer;
use app\common\server\JsonServer;
use think\facade\Db;
use think\Exception;

/***
 * Class WithdrawLogic 会员提现
 * @package app\api\logic
 */
class WithdrawLogic extends Logic
{

    /**
     * @notes 基础配置
     * @param $user_id
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/7/13 6:26 下午
     */
    public static function config($user_id)
    {

        $user = User::where('id', $user_id)->find();
        $config = [
            'able_withdraw' => $user['earnings'] ? $user['earnings'] : 0,
            'min_withdraw' => ConfigServer::get('withdraw', 'min_withdraw', 0),//最低提现金额;
            'max_withdraw' => ConfigServer::get('withdraw', 'max_withdraw', 0),//最高提现金额;
            'poundage_percent' => ConfigServer::get('withdraw', 'poundage', 0),//提现手续费;
        ];
        $type = ConfigServer::get('withdraw', 'type', 3); //提现方式,若未设置默认为提现方式微信收款码
        if ($type && is_array($type)){
            $type = array_values($type);
            foreach ($type as $key => $value){
                $config['type'][$key]['name'] = WithdrawEnum::getTypeDesc($value);
                $config['type'][$key]['value'] = $value;
            }
        }else{
            $config['type'][0]['name'] = WithdrawEnum::getTypeDesc($type);
            $config['type'][0]['value'] = $type;
        }

        return $config;
    }

    /**
     * @notes 申请提现
     * @param $post
     * @return int|string
     * @throws Exception
     * @throws \think\exception\PDOException
     * <AUTHOR>
     * @date 2021/7/13 6:26 下午
     */
    public static function apply($post)
    {
        Db::startTrans();
        try {
            $able_withdraw_list = self::getAbleWithdrawList($post);
            if (!$able_withdraw_list){
                throw new Exception('未获取可提现的订单');
            }
            if ($post['type'] == WithdrawEnum::TYPE_WECHAT_CHANGE){//提现到微信零钱
                $transfer_way = ConfigServer::get('withdraw', 'transfer_way');
                if ($transfer_way == 3){//分账
                    self::checkWechatSharing($able_withdraw_list);
                }
            }
            $able_withdraw_total = array_sum(array_column($able_withdraw_list, 'money'));
            //再次验证可提现金额
            if ($able_withdraw_total < $post['money']) {
                throw new Exception('可提现金额不足');
            }
            //提现手续费
            $poundage = 0;
            if ($post['type'] != WithdrawEnum::TYPE_BALANCE) {
                $poundage_config = ConfigServer::get('withdraw', 'poundage', 0);
                $poundage = money($post['money'] * $poundage_config / 100);
            }
            //保存提现申请
            $withdraw_id = self::saveApply($post, $able_withdraw_list, $poundage);
            Db::commit();
            return $withdraw_id;
        } catch (Exception $e) {
            Db::rollback();
            JsonServer::throw($e->getMessage());
        }
    }

    /**
     * 获取可提现的金额
     */
    public static function getAbleWithdrawList($post){
        $where_able = [
            ['dog.user_id', '=', $post['user_id']],
            ['dog.status', '=', DistributionOrderGoodsEnum::STATUS_SUCCESS],
            ['order.pay_way', 'in', PayEnum::getPayWayByKind($post['pay_way_kind'])],
        ];
        return DistributionOrderGoods::alias('dog')
            ->field('dog.*')
            ->join('order', 'dog.order_id = order.id')
            ->where($where_able)
            ->select()
            ->toArray();
    }

    protected static function saveApply($post, $able_withdraw_list, $poundage)
    {
        $time = time();
        $user_id = $post['user_id'];
        $data = [
            'sn' => createSn('withdraw_apply', 'sn'),
            'batch_no' => createSn('withdraw_apply', 'batch_no','SJZZ'),
            'user_id' => $user_id,
            'type' => $post['type'],
            'account' => $post['account'] ?? '',
            'id_front' => $post['id_front'] ?? '',
            'id_back' => $post['id_back'] ?? '',
            'id_number' => $post['id_number'] ?? '',
            'mobile' => $post['mobile'] ?? '',
            'real_name' => $post['real_name'] ?? '',
            'money' => $post['money'],
            'left_money' => money($post['money'] - $poundage),
            'money_qr_code' => $post['money_qr_code'] ?? '',
            'remark' => $post['remark'] ?? '',
            'bank' => $post['bank'] ?? '',
            'subbank' => $post['subbank'] ?? '',
            'poundage' => $poundage,
            'status' => 1, // 待提现
            'create_time' => time(),
        ];
        $withdraw_id = WithdrawApply::insertGetId($data);
        $d_id_arr = array_keys($able_withdraw_list);
        DistributionOrderGoods::update(['status'=>DistributionOrderGoodsEnum::STATUS_WITHDRAW_ING, 'update_time'=>$time, 'draw_id'=>$withdraw_id], ['id'=>$d_id_arr]);
        //提交申请后,扣减用户的佣金
        $user = User::find($user_id);
        if ($data['type'] == WithdrawEnum::TYPE_HUIFU_BANK){
            $user->partner_earnings = ['dec', $post['money']];
        }else{
            $user->earnings = ['dec', $post['money']];
        }
        $user->save();
        //增加佣金变动记录
        AccountLogLogic::AccountRecord(
            $user_id,
            $post['money'],
            2,
            AccountLog::withdraw_dec_earnings,
            '',
            $withdraw_id,
            $data['sn']
        );
        return $withdraw_id;
    }

    /**
     * @notes 提现记录
     * @param $user_id
     * @param $get
     * @param $page
     * @param $size
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/7/13 6:26 下午
     */
    public static function records($user_id, $get, $page, $size)
    {

        $count = WithdrawApply
            ::where(['user_id' => $user_id])
            ->count();

        $lists = WithdrawApply::where(['user_id' => $user_id])
            ->order('create_time desc')
            ->select();

        foreach ($lists as &$item) {
            $item['desc'] = '提现至' . WithdrawEnum::getTypeDesc($item['type']);
            $item['status_text'] = WithdrawEnum::getStatusDesc($item['status']);
        }

        $data = [
            'list' => $lists,
            'page' => $page,
            'size' => $size,
            'count' => $count,
            'more' => is_more($count, $page, $size)
        ];
        return $data;
    }

    /**
     * @notes 提现详情
     * @param $id
     * @param $user_id
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/7/13 6:26 下午
     */
    public static function info($id, $user_id)
    {

        $info = WithdrawApply::where(['id' => $id, 'user_id' => $user_id])
            ->find()->toArray();

        if (!$info) {
            return [];
        }
        $info['typeDesc'] = WithdrawEnum::getTypeDesc($info['type']);
        $info['statusDesc'] = WithdrawEnum::getStatusDesc($info['status']);
        return $info;
    }

    /**
     * Notes: 检测能否使用微信分账提现, 涉及如果不是微信支付方式则不可以;
     * Author: Darren
     * DateTime: 2023-08-29 14:35
     */
    public static function checkWechatSharing($dis_order_list){
        $order_id_arr = array_column($dis_order_list, 'order_id');
        $order_list = Order::where([['id', 'in', $order_id_arr]])->column('id,pay_way,order_sn', 'id');
        foreach ($order_list as $value){
            if ($value['pay_way'] != PayEnum::WECHAT_PAY){
                $error = '订单:'.$value['order_sn'].'不是微信支付, 不可使用微信零钱提现.';
                throw new Exception($error);
            }
        }
    }
}